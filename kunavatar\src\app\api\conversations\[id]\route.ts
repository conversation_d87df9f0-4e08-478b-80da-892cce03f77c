import { NextRequest, NextResponse } from 'next/server';
import { dbOperations } from '../../../../lib/database';
import { withAuth } from '../../../../lib/middleware/auth';
import { ApiRouteErrorHandler } from '../../../../lib/utils/apiRouteErrorHandler';
import { ValidationUtils } from '../../../../lib/utils/validationUtils';
import { DbUtils } from '../../../../lib/utils/dbUtils';

// 获取单个对话及其消息
export const GET = ApiRouteErrorHandler.wrapHandler(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  const userId = request.user!.id;
  const { id } = await params;

  // 验证ID格式
  const idValidation = ValidationUtils.validateConversationId(id);
  if (!idValidation.isValid) {
    return ApiRouteErrorHandler.handleValidationError(idValidation.error!);
  }

  const conversationId = idValidation.id!;

  // 验证访问权限并获取对话信息
  const accessCheck = DbUtils.validateConversationAccess(conversationId, userId);
  if (!accessCheck.hasAccess) {
    return ApiRouteErrorHandler.handleNotFoundError('对话');
  }

  // 获取消息
  const messagesResult = DbUtils.getConversationMessages(conversationId, userId);
  if (!messagesResult.success) {
    throw new Error(messagesResult.error);
  }

  // 获取工具调用记录
  const toolCallsResult = DbUtils.getConversationToolCalls(conversationId, userId);
  if (!toolCallsResult.success) {
    throw new Error(toolCallsResult.error);
  }

  // 获取最后使用的模型
  const lastModel = await dbOperations.getLastModelByConversationId(conversationId);

  return ApiRouteErrorHandler.createSuccessResponse({
    conversation: accessCheck.conversation,
    messages: messagesResult.messages,
    toolCallRecords: toolCallsResult.toolCalls,
    lastModel
  });
}, '获取对话');

// 更新对话标题
export const PATCH = withAuth(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const userId = request.user!.id;
    const { id } = await params;
    const conversationId = id;

    if (!conversationId) {
      return NextResponse.json(
        { error: '无效的对话ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { title }: { title: string } = body;

    if (!title || !title.trim()) {
      return NextResponse.json(
        { error: '标题不能为空' },
        { status: 400 }
      );
    }

    // 更新对话标题（验证用户权限）
    const success = dbOperations.updateConversationTitleByUserAndId(parseInt(conversationId), userId, title.trim());

    if (!success) {
      return NextResponse.json(
        { error: '对话不存在或无权限访问' },
        { status: 404 }
      );
    }

    // 获取更新后的对话
    const updatedConversation = dbOperations.getConversationByIdAndUserId(parseInt(conversationId), userId);
    
    return NextResponse.json({
      success: true,
      conversation: updatedConversation
    });
  } catch (error) {
    console.error('更新对话失败:', error);

    return NextResponse.json(
      {
        error: '更新对话失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
});

// 删除对话
export const DELETE = withAuth(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const userId = request.user!.id;
    const { id } = await params;
    const conversationId = id;

    if (!conversationId) {
      return NextResponse.json(
        { error: '无效的对话ID' },
        { status: 400 }
      );
    }

    // 删除对话（验证用户权限）
    const success = dbOperations.deleteConversationByUserAndId(parseInt(conversationId), userId);

    if (!success) {
      return NextResponse.json(
        { error: '对话不存在或无权限访问' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '对话已删除'
    });
  } catch (error) {
    console.error('删除对话失败:', error);

    return NextResponse.json(
      {
        error: '删除对话失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
});